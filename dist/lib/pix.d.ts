import { IDinamic } from "./idinamic";
import { IStatic } from "./istatic";
export declare class PIX implements IDinamic, IStatic {
    private _is_unique_transaction;
    private _key;
    private _receiver_name;
    private _receiver_city;
    private _amout;
    private _zip_code;
    private _identificator;
    private _description;
    private _location;
    private constructor();
    static static(): IStatic;
    static dinamic(): IDinamic;
    setLocation(location: string): this;
    setKey(key: string): this;
    setReceiverZipCode(zipCode: string): this;
    setReceiverName(name: string): this;
    setIdentificator(identificator: string): this;
    setDescription(description: string): this;
    setReceiverCity(city: string): this;
    setAmount(amout: number): this;
    isUniqueTransaction(is_unique_transaction: boolean): this;
    getBRCode(): string;
    getQRCode(): Promise<string | null>;
    saveQRCodeFile(out: string): Promise<unknown>;
    private _normalizeText;
    private _generateAccountInformation;
    private _additionalDataField;
    private _getEMV;
}
