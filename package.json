{"name": "gpix", "version": "1.1.8", "description": "Library written in nodejs to generate PIX br-code and qr-code.", "main": "index.js", "scripts": {"build": "tsc", "test": "tsc && mocha --reporter spec"}, "repository": {"type": "git", "url": "git+https://github.com/hiagodotme/gpix.git"}, "keywords": ["pix", "brcode", "br-code", "qrcode", "qr-code", "pagamentos", "brasil", "gerador", "gerar", "bacen", "node", "nodejs", "sdk", "easy", "programmer"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/hiagodotme/gpix/issues"}, "homepage": "https://github.com/hiagodotme/gpix#readme", "devDependencies": {"@types/qrcode": "^1.3.5", "typescript": "^4.6.2"}, "dependencies": {"chai": "^4.3.4", "mocha": "^9.1.4", "qrcode": "^1.4.4"}}